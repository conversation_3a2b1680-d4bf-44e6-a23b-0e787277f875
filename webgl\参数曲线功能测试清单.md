# 参数曲线功能测试清单

## 测试环境
- 浏览器：Chrome/Firefox/Safari/Edge 最新版本
- 分辨率：1366×768 或更高
- 服务器：Node.js Express 服务器运行在端口 62411

## 功能测试项目

### 1. 菜单集成测试
- [ ] 打开 main.html 页面
- [ ] 点击顶部标题或菜单按钮，确认下拉菜单显示
- [ ] 确认"参数曲线"菜单项存在且图标正确显示（fas fa-chart-area）
- [ ] 点击"参数曲线"菜单项
- [ ] 确认弹窗正常打开，尺寸为1366×768像素
- [ ] 确认弹窗标题显示为"参数曲线"

### 2. 页面布局测试
- [ ] 确认页面整体布局正确（左侧控制面板 + 右侧图表区域）
- [ ] 确认顶部标题栏显示"参数曲线监控"
- [ ] 确认左侧控制面板宽度为350px
- [ ] 确认右侧图表区域自适应剩余宽度
- [ ] 确认深色科技主题样式正确应用
- [ ] 确认背景动画效果正常显示

### 3. 曲线类型选择测试
- [ ] 确认左侧显示11个曲线类型选项
- [ ] 确认曲线类型包括：
  - [ ] IO状态输入1
  - [ ] IO状态输入2
  - [ ] IO状态输出1
  - [ ] IO状态输出2
  - [ ] 系统状态
  - [ ] DSP状态1
  - [ ] DSP状态2
  - [ ] DSP状态3
  - [ ] 直流电压
  - [ ] 直接电流
  - [ ] 间接电流有功
- [ ] 点击任意曲线类型，确认选中状态正确显示
- [ ] 确认复选框状态正确切换
- [ ] 确认同时只能选择一个曲线类型
- [ ] 确认鼠标悬停效果正常

### 4. 参数控制测试
- [ ] 选择开关型曲线类型（如IO状态输入1）
- [ ] 确认参数控制区域显示12个参数
- [ ] 确认每个参数都有颜色圆点标识
- [ ] 确认参数名称正确显示
- [ ] 确认开关控件正常显示
- [ ] 点击参数开关，确认状态正确切换
- [ ] 选择范围型曲线类型（如直流电压）
- [ ] 确认参数控制显示上下限输入框
- [ ] 输入数值，确认范围验证正常工作
- [ ] 确认状态指示器正确显示当前选择信息

### 5. 图表显示测试
- [ ] 选择曲线类型并激活参数后，确认右侧图表正常显示
- [ ] 确认图表标题显示"实时参数曲线"
- [ ] 确认图表控制按钮正常显示（暂停、重置、导出）
- [ ] 确认激活的参数曲线正确显示对应颜色
- [ ] 确认曲线数据实时更新（每秒更新一次）
- [ ] 确认不同参数显示不同的数据模式
- [ ] 确认图表网格和坐标轴正确显示
- [ ] 确认鼠标悬停时显示数据提示框

### 6. 图表控制功能测试
- [ ] 点击"暂停"按钮
- [ ] 确认数据更新停止
- [ ] 确认按钮文字变为"继续"
- [ ] 点击"继续"按钮
- [ ] 确认数据更新恢复
- [ ] 确认按钮文字变为"暂停"
- [ ] 点击"重置"按钮
- [ ] 确认所有曲线数据清空
- [ ] 确认所有参数选择状态重置
- [ ] 点击"导出"按钮
- [ ] 确认图表导出为PNG文件
- [ ] 确认导出文件名包含时间戳

### 7. 交互体验测试
- [ ] 确认所有按钮点击有视觉反馈
- [ ] 确认鼠标悬停效果正常
- [ ] 确认滚动条样式符合主题
- [ ] 确认左侧面板滚动功能正常
- [ ] 确认帮助信息正确显示和隐藏
- [ ] 确认状态指示器信息准确
- [ ] 确认颜色搭配协调美观

### 8. 响应式测试
- [ ] 调整浏览器窗口大小
- [ ] 确认图表自动调整大小
- [ ] 确认布局保持正确比例
- [ ] 确认在不同分辨率下显示正常

### 9. 性能测试
- [ ] 长时间运行（10分钟以上）
- [ ] 确认内存使用稳定
- [ ] 确认CPU使用率合理
- [ ] 确认数据点数量限制在100个以内
- [ ] 确认图表更新流畅无卡顿

### 10. 兼容性测试
- [ ] Chrome浏览器测试
- [ ] Firefox浏览器测试
- [ ] Safari浏览器测试（Mac）
- [ ] Edge浏览器测试
- [ ] 确认所有浏览器功能一致

### 11. 错误处理测试
- [ ] 断开网络连接
- [ ] 确认页面仍能正常显示
- [ ] 输入无效的范围值
- [ ] 确认输入验证正常工作
- [ ] 快速点击多个控件
- [ ] 确认没有JavaScript错误

### 12. 集成测试
- [ ] 从main.html主页面访问参数曲线
- [ ] 确认弹窗正常打开和关闭
- [ ] 确认与其他功能模块无冲突
- [ ] 确认样式与主系统保持一致
- [ ] 确认关闭弹窗后主页面正常

## 测试结果记录

### 通过的测试项目
- 记录所有通过的测试项目

### 发现的问题
- 记录发现的问题和错误
- 包括问题描述、重现步骤、预期结果、实际结果

### 改进建议
- 记录测试过程中发现的改进点
- 包括用户体验优化建议
- 功能增强建议

## 测试完成标准
- [ ] 所有核心功能正常工作
- [ ] 用户界面美观且符合设计要求
- [ ] 性能表现良好
- [ ] 兼容主流浏览器
- [ ] 与主系统集成无问题
- [ ] 无严重错误或异常

## 测试人员签名
测试人员：_______________
测试日期：_______________
测试版本：v1.0
测试状态：□ 通过 □ 部分通过 □ 未通过

## 备注
记录测试过程中的其他重要信息和观察结果。
