<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 参数曲线</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 参数曲线页面专用样式 */
        .parameter-curve-container {
            width: 1366px;
            height: 768px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
        }

        /* 科技感背景动画 */
        .parameter-curve-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .parameter-header {
            height: 80px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 10;
        }

        .parameter-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .parameter-title i {
            color: var(--accent-color);
            font-size: 32px;
        }

        /* 主内容区域 */
        .parameter-main-content {
            height: calc(100% - 80px);
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        /* 左侧控制面板 */
        .parameter-control-panel {
            width: 350px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        /* 时间筛选区域 */
        .time-filter-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: var(--accent-color);
        }

        .time-mode-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .time-mode-btn {
            flex: 1;
            padding: 8px 12px;
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 6px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            text-align: center;
        }

        .time-mode-btn:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--accent-color);
        }

        .time-mode-btn.active {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--accent-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .history-time-selector {
            display: none;
        }

        .history-time-selector.show {
            display: block;
        }

        .history-time-select {
            width: 100%;
            padding: 8px 12px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 12px;
        }

        .history-time-select:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        /* 曲线类型选择区域 */
        .curve-type-section {
            margin-bottom: 25px;
        }

        .curve-type-select {
            width: 100%;
            padding: 10px 12px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 14px;
            cursor: pointer;
        }

        .curve-type-select:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .curve-type-select option {
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: 8px;
        }

        /* 参数控制区域 */
        .parameter-control-section {
            margin-bottom: 25px;
        }

        .parameter-list {
            display: none;
            flex-direction: column;
            gap: 12px;
        }

        .parameter-list.show {
            display: flex;
        }

        .parameter-item {
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
        }

        .parameter-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .parameter-color-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .parameter-name {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .parameter-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .parameter-switch {
            width: 40px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .parameter-switch.active {
            background: var(--accent-color);
        }

        .parameter-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .parameter-switch.active::after {
            transform: translateX(20px);
        }

        /* 右侧图表区域 */
        .parameter-chart-area {
            flex: 1;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-control-btn {
            padding: 8px 16px;
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid var(--accent-color);
            border-radius: 6px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .chart-control-btn:hover {
            background: rgba(0, 212, 255, 0.3);
            transform: translateY(-2px);
        }

        .chart-container {
            flex: 1;
            min-height: 500px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        /* 滚动条样式 */
        .parameter-control-panel::-webkit-scrollbar {
            width: 6px;
        }

        .parameter-control-panel::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .parameter-control-panel::-webkit-scrollbar-thumb {
            background: var(--accent-color);
            border-radius: 3px;
        }

        .parameter-control-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 212, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="parameter-curve-container">
        <!-- 顶部标题栏 -->
        <header class="parameter-header" style="display: none;">
            <h1 class="parameter-title">
                <i class="fas fa-chart-area"></i>
                参数曲线监控
            </h1>
        </header>

        <!-- 主内容区域 -->
        <main class="parameter-main-content">
            <!-- 左侧控制面板 -->
            <aside class="parameter-control-panel">
                <!-- 时间筛选 -->
                <div class="time-filter-section">
                    <h3 class="section-title">
                        <i class="fas fa-clock"></i>
                        时间筛选
                    </h3>
                    <div class="time-mode-selector">
                        <button class="time-mode-btn active" id="realtimeMode" onclick="switchTimeMode('realtime')">
                            实时模式
                        </button>
                        <button class="time-mode-btn" id="historyMode" onclick="switchTimeMode('history')">
                            历史模式
                        </button>
                    </div>
                    <div class="history-time-selector" id="historyTimeSelector">
                        <select class="history-time-select" id="historyTimeSelect" onchange="updateHistoryTime()">
                            <option value="">请选择历史时间点</option>
                            <option value="2025-07-07-14:30:00">2025-07-07 14:30:00</option>
                            <option value="2025-07-07-13:15:00">2025-07-07 13:15:00</option>
                            <option value="2025-07-07-12:00:00">2025-07-07 12:00:00</option>
                            <option value="2025-07-07-10:45:00">2025-07-07 10:45:00</option>
                            <option value="2025-07-07-09:30:00">2025-07-07 09:30:00</option>
                            <option value="2025-07-06-16:20:00">2025-07-06 16:20:00</option>
                            <option value="2025-07-06-14:10:00">2025-07-06 14:10:00</option>
                            <option value="2025-07-06-11:55:00">2025-07-06 11:55:00</option>
                        </select>
                    </div>
                </div>

                <!-- 曲线类型选择 -->
                <div class="curve-type-section">
                    <h3 class="section-title">
                        <i class="fas fa-list"></i>
                        曲线类型选择
                    </h3>
                    <select class="curve-type-select" id="curveTypeSelect" onchange="selectCurveType()">
                        <option value="">请选择曲线类型</option>
                        <!-- 曲线类型选项将通过JavaScript动态生成 -->
                    </select>
                </div>

                <!-- 参数控制 -->
                <div class="parameter-control-section">
                    <h3 class="section-title">
                        <i class="fas fa-sliders-h"></i>
                        参数控制
                        <span class="parameter-count" id="parameterCount" style="font-size: 12px; color: var(--text-secondary); margin-left: 10px;">
                            (未选择曲线类型)
                        </span>
                    </h3>
                    <div class="parameter-list" id="parameterList">
                        <!-- 参数控制项将通过JavaScript动态生成 -->
                    </div>
                    <div class="parameter-help" id="parameterHelp" style="
                        padding: 15px;
                        background: rgba(0, 212, 255, 0.1);
                        border: 1px solid rgba(0, 212, 255, 0.3);
                        border-radius: 8px;
                        margin-top: 15px;
                        font-size: 12px;
                        color: var(--text-secondary);
                        line-height: 1.4;
                    ">
                        <i class="fas fa-info-circle" style="color: var(--accent-color); margin-right: 5px;"></i>
                        请先选择一个曲线类型，然后使用开关控制参数的显示/隐藏。所有参数都为数字信号（0/1状态）。
                    </div>
                </div>
            </aside>

            <!-- 右侧图表区域 -->
            <section class="parameter-chart-area">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        实时参数曲线
                    </h3>
                    <div class="chart-controls">
                        <button class="chart-control-btn" onclick="pauseChart()">
                            <i class="fas fa-pause"></i> 暂停
                        </button>
                        <button class="chart-control-btn" onclick="resetChart()">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                        <button class="chart-control-btn" onclick="exportChart()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                </div>
                <div class="chart-container" id="parameterChart">
                    <!-- ECharts图表将在这里渲染 -->
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 参数曲线页面脚本
         * 处理曲线类型选择、参数控制和图表显示
         */

        // 全局变量
        let parameterChart = null;
        let chartUpdateInterval = null;
        let chartData = {};
        let isPaused = false;
        let currentTimeMode = 'realtime'; // 'realtime' 或 'history'
        let selectedHistoryTime = null;

        // 曲线类型配置（现在都是开关型）
        const curveTypes = [
            { id: 'io-input-1', name: 'IO状态输入1' },
            { id: 'io-input-2', name: 'IO状态输入2' },
            { id: 'io-output-1', name: 'IO状态输出1' },
            { id: 'io-output-2', name: 'IO状态输出2' },
            { id: 'system-status', name: '系统状态' },
            { id: 'dsp-status-1', name: 'DSP状态1' },
            { id: 'dsp-status-2', name: 'DSP状态2' },
            { id: 'dsp-status-3', name: 'DSP状态3' },
            { id: 'dc-voltage', name: '直流电压' },
            { id: 'direct-current', name: '直接电流' },
            { id: 'indirect-current', name: '间接电流有功' }
        ];

        // 参数配置（当选择特定曲线类型后显示）
        const parameters = [
            { id: 'manual-reactive', name: '手动给定无功', color: '#ff6b6b' },
            { id: 'pll-mode', name: '锁相环模式', color: '#4ecdc4' },
            { id: 'control-mode', name: '控制模式选择', color: '#45b7d1' },
            { id: 'low-voltage-support', name: '低电压无功支撑', color: '#96ceb4' },
            { id: 'unit-level', name: '单元级数', color: '#feca57' },
            { id: 'ct-sampling', name: 'CT采样点位置', color: '#ff9ff3' },
            { id: 'phase-number', name: '相数', color: '#54a0ff' },
            { id: 'master-slave', name: '主从机模式', color: '#5f27cd' },
            { id: 'svg-connection', name: 'SVG连接方式', color: '#00d2d3' },
            { id: 'balance-control', name: '平衡控制使能', color: '#ff6348' },
            { id: 'voltage-feedforward', name: '电压前馈模式', color: '#2ed573' },
            { id: 'idle-mode', name: '空载模式使能', color: '#ffa502' }
        ];

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initParameterCurvePage();
        });

        /**
         * 初始化参数曲线页面
         */
        function initParameterCurvePage() {
            console.log('初始化参数曲线页面...');
            
            // 初始化曲线类型选择
            initCurveTypeSelection();
            
            // 初始化图表
            initParameterChart();
            
            // 开始数据更新
            startDataUpdate();
        }

        /**
         * 初始化曲线类型选择
         */
        function initCurveTypeSelection() {
            const select = document.getElementById('curveTypeSelect');
            if (!select) return;

            // 清空现有选项（保留默认选项）
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }

            // 添加曲线类型选项
            curveTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id;
                option.textContent = type.name;
                select.appendChild(option);
            });
        }

        /**
         * 选择曲线类型（下拉框选择）
         */
        function selectCurveType() {
            const select = document.getElementById('curveTypeSelect');
            if (!select) return;

            const selectedTypeId = select.value;

            if (selectedTypeId) {
                // 清空之前的图表数据
                resetChart();
                // 显示对应的参数控制
                showParameterControls(selectedTypeId);
            } else {
                // 未选择任何类型，隐藏参数控制
                hideParameterControls();
            }
        }

        /**
         * 切换时间模式
         * @param {string} mode - 时间模式 ('realtime' 或 'history')
         */
        function switchTimeMode(mode) {
            currentTimeMode = mode;

            // 更新按钮状态
            document.getElementById('realtimeMode').classList.toggle('active', mode === 'realtime');
            document.getElementById('historyMode').classList.toggle('active', mode === 'history');

            // 显示/隐藏历史时间选择器
            const historySelector = document.getElementById('historyTimeSelector');
            if (historySelector) {
                historySelector.classList.toggle('show', mode === 'history');
            }

            // 重置历史时间选择
            if (mode === 'realtime') {
                selectedHistoryTime = null;
                const historySelect = document.getElementById('historyTimeSelect');
                if (historySelect) {
                    historySelect.value = '';
                }
            }

            // 更新图表数据
            updateChartForTimeMode();
        }

        /**
         * 更新历史时间选择
         */
        function updateHistoryTime() {
            const select = document.getElementById('historyTimeSelect');
            if (!select) return;

            selectedHistoryTime = select.value;
            updateChartForTimeMode();
        }

        /**
         * 根据时间模式更新图表
         */
        function updateChartForTimeMode() {
            // 如果有激活的参数，重新生成对应时间模式的数据
            Object.keys(chartData).forEach(paramId => {
                chartData[paramId] = [];
            });

            console.log(`切换到${currentTimeMode}模式${selectedHistoryTime ? ', 时间点: ' + selectedHistoryTime : ''}`);
        }

        /**
         * 显示参数控制
         * @param {string} typeId - 曲线类型ID
         */
        function showParameterControls(typeId) {
            const parameterList = document.getElementById('parameterList');
            const parameterCount = document.getElementById('parameterCount');

            if (!parameterList) return;

            const curveType = curveTypes.find(type => type.id === typeId);
            if (!curveType) return;

            parameterList.innerHTML = '';

            // 显示所有参数（现在都是开关型控制）
            parameters.forEach(param => {
                const item = document.createElement('div');
                item.className = 'parameter-item';

                item.innerHTML = `
                    <div class="parameter-header">
                        <div class="parameter-color-dot" style="background-color: ${param.color}"></div>
                        <span class="parameter-name">${param.name}</span>
                    </div>
                    <div class="parameter-controls">
                        <div class="parameter-switch" onclick="toggleParameterSwitch('${param.id}')"></div>
                    </div>
                `;

                parameterList.appendChild(item);
            });

            // 更新状态指示器
            if (parameterCount) {
                parameterCount.textContent = `(${curveType.name} - ${parameters.length}个参数)`;
            }

            // 隐藏帮助信息
            const parameterHelp = document.getElementById('parameterHelp');
            if (parameterHelp) {
                parameterHelp.style.display = 'none';
            }

            parameterList.classList.add('show');
        }

        /**
         * 隐藏参数控制
         */
        function hideParameterControls() {
            const parameterList = document.getElementById('parameterList');
            const parameterCount = document.getElementById('parameterCount');
            const parameterHelp = document.getElementById('parameterHelp');

            if (parameterList) {
                parameterList.classList.remove('show');
            }

            if (parameterCount) {
                parameterCount.textContent = '(未选择曲线类型)';
            }

            // 显示帮助信息
            if (parameterHelp) {
                parameterHelp.style.display = 'block';
            }
        }

        /**
         * 切换参数开关状态
         * @param {string} paramId - 参数ID
         */
        function toggleParameterSwitch(paramId) {
            const switchEl = event.target;
            switchEl.classList.toggle('active');

            // 更新图表数据
            updateChartSeries(paramId, switchEl.classList.contains('active'));
        }

        /**
         * 初始化参数图表
         */
        function initParameterChart() {
            const chartContainer = document.getElementById('parameterChart');
            if (!chartContainer) return;
            
            parameterChart = echarts.init(chartContainer);
            
            const option = {
                backgroundColor: 'transparent',
                grid: {
                    left: '5%',
                    right: '5%',
                    top: '10%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'time',
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#ffffff',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.2)' }
                    }
                },
                yAxis: {
                    type: 'value',
                    min: -0.2,
                    max: 1.2,
                    interval: 0.5,
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#ffffff',
                        fontSize: 12,
                        formatter: function(value) {
                            if (value === 0) return '关闭 (0)';
                            if (value === 1) return '开启 (1)';
                            return value;
                        }
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.2)' }
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(26, 31, 46, 0.9)',
                    borderColor: 'rgba(0, 212, 255, 0.5)',
                    textStyle: { color: '#ffffff' },
                    formatter: function(params) {
                        let result = params[0].axisValueLabel + '<br/>';
                        params.forEach(param => {
                            const status = param.value[1] === 1 ? '开启' : '关闭';
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${status} (${param.value[1]})<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    textStyle: { color: '#ffffff' },
                    top: '5%'
                },
                series: []
            };
            
            parameterChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', () => {
                if (parameterChart) {
                    parameterChart.resize();
                }
            });
        }

        /**
         * 更新图表系列数据
         * @param {string} paramId - 参数ID
         * @param {boolean} isActive - 是否激活
         */
        function updateChartSeries(paramId, isActive) {
            if (!parameterChart) return;
            
            const param = parameters.find(p => p.id === paramId);
            if (!param) return;
            
            const option = parameterChart.getOption();
            
            if (isActive) {
                // 添加新的数据系列
                if (!chartData[paramId]) {
                    chartData[paramId] = [];
                }
                
                const newSeries = {
                    name: param.name,
                    type: 'line',
                    data: chartData[paramId],
                    lineStyle: { color: param.color },
                    itemStyle: { color: param.color },
                    smooth: true,
                    symbol: 'none'
                };
                
                option.series.push(newSeries);
            } else {
                // 移除数据系列
                option.series = option.series.filter(series => series.name !== param.name);
                delete chartData[paramId];
            }
            
            parameterChart.setOption(option);
        }

        /**
         * 开始数据更新
         */
        function startDataUpdate() {
            chartUpdateInterval = setInterval(() => {
                if (isPaused) return;

                const now = new Date();

                // 为每个激活的参数生成0/1数字信号
                Object.keys(chartData).forEach(paramId => {
                    let value;

                    if (currentTimeMode === 'realtime') {
                        // 实时模式：生成动态的0/1信号
                        const timeOffset = now.getTime() / 1000;
                        const paramIndex = Object.keys(chartData).indexOf(paramId);

                        // 根据参数生成不同频率的0/1信号
                        switch(paramId) {
                            case 'manual-reactive':
                                value = Math.sin(timeOffset / 3 + paramIndex) > 0 ? 1 : 0;
                                break;
                            case 'pll-mode':
                                value = Math.cos(timeOffset / 4 + paramIndex) > 0.3 ? 1 : 0;
                                break;
                            case 'control-mode':
                                value = Math.sin(timeOffset / 2.5 + paramIndex) > -0.2 ? 1 : 0;
                                break;
                            case 'low-voltage-support':
                                value = Math.cos(timeOffset / 5 + paramIndex) > 0.5 ? 1 : 0;
                                break;
                            case 'unit-level':
                                value = Math.sin(timeOffset / 3.5 + paramIndex) > 0.1 ? 1 : 0;
                                break;
                            case 'ct-sampling':
                                value = Math.cos(timeOffset / 2.8 + paramIndex) > -0.1 ? 1 : 0;
                                break;
                            case 'phase-number':
                                value = Math.sin(timeOffset / 4.2 + paramIndex) > 0.4 ? 1 : 0;
                                break;
                            case 'master-slave':
                                value = Math.cos(timeOffset / 3.8 + paramIndex) > 0.2 ? 1 : 0;
                                break;
                            case 'svg-connection':
                                value = Math.sin(timeOffset / 2.2 + paramIndex) > -0.3 ? 1 : 0;
                                break;
                            case 'balance-control':
                                value = Math.cos(timeOffset / 4.5 + paramIndex) > 0.6 ? 1 : 0;
                                break;
                            case 'voltage-feedforward':
                                value = Math.sin(timeOffset / 3.2 + paramIndex) > 0 ? 1 : 0;
                                break;
                            case 'idle-mode':
                                value = Math.cos(timeOffset / 5.5 + paramIndex) > 0.3 ? 1 : 0;
                                break;
                            default:
                                value = Math.random() > 0.5 ? 1 : 0;
                        }
                    } else {
                        // 历史模式：生成固定的历史数据模式
                        const historyPattern = getHistoryPattern(paramId, selectedHistoryTime);
                        const timeIndex = Math.floor((now.getTime() / 1000) % historyPattern.length);
                        value = historyPattern[timeIndex];
                    }

                    chartData[paramId].push([now, value]);

                    // 保持数据点数量在合理范围内
                    if (chartData[paramId].length > 100) {
                        chartData[paramId].shift();
                    }
                });

                // 更新图表
                if (parameterChart && Object.keys(chartData).length > 0) {
                    const option = parameterChart.getOption();
                    const seriesData = Object.keys(chartData).map(paramId => {
                        const param = parameters.find(p => p.id === paramId);
                        return {
                            name: param ? param.name : paramId,
                            type: 'line',
                            data: chartData[paramId],
                            lineStyle: {
                                color: param ? param.color : '#ffffff',
                                width: 2
                            },
                            itemStyle: { color: param ? param.color : '#ffffff' },
                            smooth: false, // 数字信号不需要平滑
                            symbol: 'circle',
                            symbolSize: 4,
                            animation: false,
                            step: 'end' // 阶梯线显示，更适合数字信号
                        };
                    });

                    option.series = seriesData;
                    parameterChart.setOption(option, false, true);
                }
            }, 1000);
        }

        /**
         * 获取历史数据模式
         * @param {string} paramId - 参数ID
         * @param {string} historyTime - 历史时间点
         * @returns {Array} 历史数据模式数组
         */
        function getHistoryPattern(paramId, historyTime) {
            // 根据不同的历史时间点和参数生成不同的固定模式
            const patterns = {
                'manual-reactive': [1, 1, 0, 1, 0, 0, 1, 1, 0, 1],
                'pll-mode': [0, 1, 1, 1, 0, 1, 0, 0, 1, 1],
                'control-mode': [1, 0, 1, 0, 1, 1, 0, 1, 0, 0],
                'low-voltage-support': [0, 0, 1, 1, 1, 0, 1, 0, 1, 0],
                'unit-level': [1, 1, 1, 0, 0, 1, 0, 1, 1, 0],
                'ct-sampling': [0, 1, 0, 1, 1, 0, 0, 1, 0, 1],
                'phase-number': [1, 0, 0, 1, 1, 1, 0, 0, 1, 1],
                'master-slave': [0, 0, 1, 0, 1, 1, 1, 0, 0, 1],
                'svg-connection': [1, 1, 0, 0, 0, 1, 1, 1, 0, 0],
                'balance-control': [0, 1, 1, 0, 1, 0, 1, 1, 0, 1],
                'voltage-feedforward': [1, 0, 1, 1, 0, 0, 1, 0, 1, 0],
                'idle-mode': [0, 0, 0, 1, 1, 1, 0, 1, 1, 1]
            };

            return patterns[paramId] || [0, 1, 0, 1, 0, 1, 0, 1, 0, 1];
        }

        /**
         * 暂停/恢复图表更新
         */
        function pauseChart() {
            isPaused = !isPaused;
            const btn = event.target.closest('.chart-control-btn');
            if (btn) {
                const icon = btn.querySelector('i');
                const text = btn.querySelector('span') || btn.childNodes[1];
                
                if (isPaused) {
                    icon.className = 'fas fa-play';
                    if (text) text.textContent = ' 继续';
                } else {
                    icon.className = 'fas fa-pause';
                    if (text) text.textContent = ' 暂停';
                }
            }
        }

        /**
         * 重置图表数据
         */
        function resetChart() {
            chartData = {};
            if (parameterChart) {
                const option = parameterChart.getOption();
                option.series = [];
                parameterChart.setOption(option);
            }

            // 重置曲线类型选择
            const curveTypeSelect = document.getElementById('curveTypeSelect');
            if (curveTypeSelect) {
                curveTypeSelect.value = '';
            }

            // 重置所有参数开关状态
            document.querySelectorAll('.parameter-switch.active').forEach(el => {
                el.classList.remove('active');
            });

            // 隐藏参数控制
            hideParameterControls();
        }

        /**
         * 导出图表数据
         */
        function exportChart() {
            if (!parameterChart) return;
            
            // 导出为图片
            const url = parameterChart.getDataURL({
                type: 'png',
                backgroundColor: '#1a1f2e'
            });
            
            const link = document.createElement('a');
            link.download = `参数曲线_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`;
            link.href = url;
            link.click();
        }
    </script>
</body>
</html>
