<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 参数曲线</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 参数曲线页面专用样式 */
        .parameter-curve-container {
            width: 1366px;
            height: 768px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
        }

        /* 科技感背景动画 */
        .parameter-curve-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .parameter-header {
            height: 80px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 10;
        }

        .parameter-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .parameter-title i {
            color: var(--accent-color);
            font-size: 32px;
        }

        /* 主内容区域 */
        .parameter-main-content {
            height: calc(100% - 80px);
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        /* 左侧控制面板 */
        .parameter-control-panel {
            width: 350px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        /* 曲线类型选择区域 */
        .curve-type-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: var(--accent-color);
        }

        .curve-type-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .curve-type-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 12px;
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .curve-type-item:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--accent-color);
            transform: translateX(5px);
        }

        .curve-type-item.active {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--accent-color);
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        }

        .curve-type-checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid var(--accent-color);
            border-radius: 4px;
            position: relative;
            background: transparent;
            cursor: pointer;
        }

        .curve-type-checkbox.checked {
            background: var(--accent-color);
        }

        .curve-type-checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--bg-primary);
            font-size: 12px;
            font-weight: bold;
        }

        .curve-type-label {
            flex: 1;
            font-size: 14px;
            color: var(--text-primary);
        }

        /* 参数控制区域 */
        .parameter-control-section {
            margin-bottom: 25px;
        }

        .parameter-list {
            display: none;
            flex-direction: column;
            gap: 12px;
        }

        .parameter-list.show {
            display: flex;
        }

        .parameter-item {
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
        }

        .parameter-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .parameter-color-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .parameter-name {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .parameter-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .parameter-switch {
            width: 40px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .parameter-switch.active {
            background: var(--accent-color);
        }

        .parameter-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .parameter-switch.active::after {
            transform: translateX(20px);
        }

        .parameter-range-inputs {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .range-input {
            width: 60px;
            padding: 4px 6px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: var(--text-primary);
            font-size: 12px;
            text-align: center;
        }

        .range-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .range-separator {
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* 右侧图表区域 */
        .parameter-chart-area {
            flex: 1;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-control-btn {
            padding: 8px 16px;
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid var(--accent-color);
            border-radius: 6px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .chart-control-btn:hover {
            background: rgba(0, 212, 255, 0.3);
            transform: translateY(-2px);
        }

        .chart-container {
            flex: 1;
            min-height: 500px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        /* 滚动条样式 */
        .parameter-control-panel::-webkit-scrollbar {
            width: 6px;
        }

        .parameter-control-panel::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .parameter-control-panel::-webkit-scrollbar-thumb {
            background: var(--accent-color);
            border-radius: 3px;
        }

        .parameter-control-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 212, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="parameter-curve-container">
        <!-- 顶部标题栏 -->
        <header class="parameter-header" style="display: none;">
            <h1 class="parameter-title">
                <i class="fas fa-chart-area"></i>
                参数曲线监控
            </h1>
        </header>

        <!-- 主内容区域 -->
        <main class="parameter-main-content">
            <!-- 左侧控制面板 -->
            <aside class="parameter-control-panel">
                <!-- 曲线类型选择 -->
                <div class="curve-type-section">
                    <h3 class="section-title">
                        <i class="fas fa-list"></i>
                        曲线类型选择
                    </h3>
                    <div class="curve-type-grid" id="curveTypeGrid">
                        <!-- 曲线类型项将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 参数控制 -->
                <div class="parameter-control-section">
                    <h3 class="section-title">
                        <i class="fas fa-sliders-h"></i>
                        参数控制
                        <span class="parameter-count" id="parameterCount" style="font-size: 12px; color: var(--text-secondary); margin-left: 10px;">
                            (未选择曲线类型)
                        </span>
                    </h3>
                    <div class="parameter-list" id="parameterList">
                        <!-- 参数控制项将通过JavaScript动态生成 -->
                    </div>
                    <div class="parameter-help" id="parameterHelp" style="
                        padding: 15px;
                        background: rgba(0, 212, 255, 0.1);
                        border: 1px solid rgba(0, 212, 255, 0.3);
                        border-radius: 8px;
                        margin-top: 15px;
                        font-size: 12px;
                        color: var(--text-secondary);
                        line-height: 1.4;
                    ">
                        <i class="fas fa-info-circle" style="color: var(--accent-color); margin-right: 5px;"></i>
                        请先选择一个曲线类型，然后配置相应的参数控制方式。开关型参数可直接控制显示/隐藏，范围型参数需要设置上下限值。
                    </div>
                </div>
            </aside>

            <!-- 右侧图表区域 -->
            <section class="parameter-chart-area">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        实时参数曲线
                    </h3>
                    <div class="chart-controls">
                        <button class="chart-control-btn" onclick="pauseChart()">
                            <i class="fas fa-pause"></i> 暂停
                        </button>
                        <button class="chart-control-btn" onclick="resetChart()">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                        <button class="chart-control-btn" onclick="exportChart()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                </div>
                <div class="chart-container" id="parameterChart">
                    <!-- ECharts图表将在这里渲染 -->
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 参数曲线页面脚本
         * 处理曲线类型选择、参数控制和图表显示
         */

        // 全局变量
        let parameterChart = null;
        let chartUpdateInterval = null;
        let chartData = {};
        let isPaused = false;

        // 曲线类型配置
        const curveTypes = [
            { id: 'io-input-1', name: 'IO状态输入1', type: 'switch' },
            { id: 'io-input-2', name: 'IO状态输入2', type: 'switch' },
            { id: 'io-output-1', name: 'IO状态输出1', type: 'switch' },
            { id: 'io-output-2', name: 'IO状态输出2', type: 'switch' },
            { id: 'system-status', name: '系统状态', type: 'switch' },
            { id: 'dsp-status-1', name: 'DSP状态1', type: 'switch' },
            { id: 'dsp-status-2', name: 'DSP状态2', type: 'switch' },
            { id: 'dsp-status-3', name: 'DSP状态3', type: 'switch' },
            { id: 'dc-voltage', name: '直流电压', type: 'range' },
            { id: 'direct-current', name: '直接电流', type: 'range' },
            { id: 'indirect-current', name: '间接电流有功', type: 'range' }
        ];

        // 参数配置（当选择特定曲线类型后显示）
        const parameters = [
            { id: 'manual-reactive', name: '手动给定无功', color: '#ff6b6b' },
            { id: 'pll-mode', name: '锁相环模式', color: '#4ecdc4' },
            { id: 'control-mode', name: '控制模式选择', color: '#45b7d1' },
            { id: 'low-voltage-support', name: '低电压无功支撑', color: '#96ceb4' },
            { id: 'unit-level', name: '单元级数', color: '#feca57' },
            { id: 'ct-sampling', name: 'CT采样点位置', color: '#ff9ff3' },
            { id: 'phase-number', name: '相数', color: '#54a0ff' },
            { id: 'master-slave', name: '主从机模式', color: '#5f27cd' },
            { id: 'svg-connection', name: 'SVG连接方式', color: '#00d2d3' },
            { id: 'balance-control', name: '平衡控制使能', color: '#ff6348' },
            { id: 'voltage-feedforward', name: '电压前馈模式', color: '#2ed573' },
            { id: 'idle-mode', name: '空载模式使能', color: '#ffa502' }
        ];

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initParameterCurvePage();
        });

        /**
         * 初始化参数曲线页面
         */
        function initParameterCurvePage() {
            console.log('初始化参数曲线页面...');
            
            // 初始化曲线类型选择
            initCurveTypeSelection();
            
            // 初始化图表
            initParameterChart();
            
            // 开始数据更新
            startDataUpdate();
        }

        /**
         * 初始化曲线类型选择
         */
        function initCurveTypeSelection() {
            const grid = document.getElementById('curveTypeGrid');
            if (!grid) return;

            grid.innerHTML = '';
            
            curveTypes.forEach(type => {
                const item = document.createElement('div');
                item.className = 'curve-type-item';
                item.dataset.typeId = type.id;
                item.dataset.controlType = type.type;
                
                item.innerHTML = `
                    <div class="curve-type-checkbox" id="checkbox-${type.id}"></div>
                    <span class="curve-type-label">${type.name}</span>
                `;
                
                item.addEventListener('click', () => toggleCurveType(type.id));
                grid.appendChild(item);
            });
        }

        /**
         * 切换曲线类型选择状态
         * @param {string} typeId - 曲线类型ID
         */
        function toggleCurveType(typeId) {
            const item = document.querySelector(`[data-type-id="${typeId}"]`);
            const checkbox = document.getElementById(`checkbox-${typeId}`);
            
            if (!item || !checkbox) return;
            
            const isActive = item.classList.contains('active');
            
            if (isActive) {
                // 取消选择
                item.classList.remove('active');
                checkbox.classList.remove('checked');
                hideParameterControls();
            } else {
                // 清除其他选择
                document.querySelectorAll('.curve-type-item.active').forEach(el => {
                    el.classList.remove('active');
                });
                document.querySelectorAll('.curve-type-checkbox.checked').forEach(el => {
                    el.classList.remove('checked');
                });
                
                // 选择当前项
                item.classList.add('active');
                checkbox.classList.add('checked');
                
                // 显示对应的参数控制
                showParameterControls(typeId);
            }
        }

        /**
         * 显示参数控制
         * @param {string} typeId - 曲线类型ID
         */
        function showParameterControls(typeId) {
            const parameterList = document.getElementById('parameterList');
            const parameterCount = document.getElementById('parameterCount');

            if (!parameterList) return;

            const curveType = curveTypes.find(type => type.id === typeId);
            if (!curveType) return;

            parameterList.innerHTML = '';

            // 根据曲线类型显示对应的参数
            parameters.forEach(param => {
                const item = document.createElement('div');
                item.className = 'parameter-item';

                const controlsHtml = curveType.type === 'switch'
                    ? `<div class="parameter-switch" onclick="toggleParameterSwitch('${param.id}')"></div>`
                    : `<div class="parameter-range-inputs">
                        <input type="number" class="range-input" placeholder="下限" id="min-${param.id}"
                               onchange="updateParameterRange('${param.id}')" value="0">
                        <span class="range-separator">~</span>
                        <input type="number" class="range-input" placeholder="上限" id="max-${param.id}"
                               onchange="updateParameterRange('${param.id}')" value="100">
                       </div>`;

                item.innerHTML = `
                    <div class="parameter-header">
                        <div class="parameter-color-dot" style="background-color: ${param.color}"></div>
                        <span class="parameter-name">${param.name}</span>
                    </div>
                    <div class="parameter-controls">
                        ${controlsHtml}
                    </div>
                `;

                parameterList.appendChild(item);
            });

            // 更新状态指示器
            if (parameterCount) {
                parameterCount.textContent = `(${curveType.name} - ${parameters.length}个参数)`;
            }

            // 隐藏帮助信息
            const parameterHelp = document.getElementById('parameterHelp');
            if (parameterHelp) {
                parameterHelp.style.display = 'none';
            }

            parameterList.classList.add('show');
        }

        /**
         * 隐藏参数控制
         */
        function hideParameterControls() {
            const parameterList = document.getElementById('parameterList');
            const parameterCount = document.getElementById('parameterCount');
            const parameterHelp = document.getElementById('parameterHelp');

            if (parameterList) {
                parameterList.classList.remove('show');
            }

            if (parameterCount) {
                parameterCount.textContent = '(未选择曲线类型)';
            }

            // 显示帮助信息
            if (parameterHelp) {
                parameterHelp.style.display = 'block';
            }
        }

        /**
         * 切换参数开关状态
         * @param {string} paramId - 参数ID
         */
        function toggleParameterSwitch(paramId) {
            const switchEl = event.target;
            switchEl.classList.toggle('active');

            // 更新图表数据
            updateChartSeries(paramId, switchEl.classList.contains('active'));
        }

        /**
         * 更新参数范围设置
         * @param {string} paramId - 参数ID
         */
        function updateParameterRange(paramId) {
            const minInput = document.getElementById(`min-${paramId}`);
            const maxInput = document.getElementById(`max-${paramId}`);

            if (!minInput || !maxInput) return;

            const minValue = parseFloat(minInput.value) || 0;
            const maxValue = parseFloat(maxInput.value) || 100;

            // 确保最小值不大于最大值
            if (minValue >= maxValue) {
                maxInput.value = minValue + 10;
            }

            // 如果参数已经在图表中显示，更新其数据范围
            if (chartData[paramId]) {
                console.log(`更新参数 ${paramId} 的范围: ${minValue} ~ ${maxValue}`);
                // 这里可以添加更多的范围控制逻辑
            }
        }

        /**
         * 初始化参数图表
         */
        function initParameterChart() {
            const chartContainer = document.getElementById('parameterChart');
            if (!chartContainer) return;
            
            parameterChart = echarts.init(chartContainer);
            
            const option = {
                backgroundColor: 'transparent',
                grid: {
                    left: '5%',
                    right: '5%',
                    top: '10%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'time',
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#ffffff',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.2)' }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#ffffff',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.2)' }
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(26, 31, 46, 0.9)',
                    borderColor: 'rgba(0, 212, 255, 0.5)',
                    textStyle: { color: '#ffffff' }
                },
                legend: {
                    textStyle: { color: '#ffffff' },
                    top: '5%'
                },
                series: []
            };
            
            parameterChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', () => {
                if (parameterChart) {
                    parameterChart.resize();
                }
            });
        }

        /**
         * 更新图表系列数据
         * @param {string} paramId - 参数ID
         * @param {boolean} isActive - 是否激活
         */
        function updateChartSeries(paramId, isActive) {
            if (!parameterChart) return;
            
            const param = parameters.find(p => p.id === paramId);
            if (!param) return;
            
            const option = parameterChart.getOption();
            
            if (isActive) {
                // 添加新的数据系列
                if (!chartData[paramId]) {
                    chartData[paramId] = [];
                }
                
                const newSeries = {
                    name: param.name,
                    type: 'line',
                    data: chartData[paramId],
                    lineStyle: { color: param.color },
                    itemStyle: { color: param.color },
                    smooth: true,
                    symbol: 'none'
                };
                
                option.series.push(newSeries);
            } else {
                // 移除数据系列
                option.series = option.series.filter(series => series.name !== param.name);
                delete chartData[paramId];
            }
            
            parameterChart.setOption(option);
        }

        /**
         * 开始数据更新
         */
        function startDataUpdate() {
            chartUpdateInterval = setInterval(() => {
                if (isPaused) return;

                const now = new Date();

                // 为每个激活的参数生成更真实的模拟数据
                Object.keys(chartData).forEach(paramId => {
                    const param = parameters.find(p => p.id === paramId);
                    let value;

                    // 根据参数类型生成不同的数据模式
                    switch(paramId) {
                        case 'manual-reactive':
                            value = Math.sin(now.getTime() / 2000) * 30 + 50 + Math.random() * 5;
                            break;
                        case 'pll-mode':
                            value = Math.cos(now.getTime() / 3000) * 20 + 25 + Math.random() * 3;
                            break;
                        case 'control-mode':
                            value = Math.sin(now.getTime() / 1500 + Math.PI/4) * 40 + 60 + Math.random() * 8;
                            break;
                        case 'low-voltage-support':
                            value = Math.abs(Math.sin(now.getTime() / 4000)) * 80 + 10 + Math.random() * 5;
                            break;
                        case 'unit-level':
                            value = Math.sin(now.getTime() / 2500) * 15 + 35 + Math.random() * 2;
                            break;
                        case 'ct-sampling':
                            value = Math.cos(now.getTime() / 1800) * 25 + 45 + Math.random() * 4;
                            break;
                        case 'phase-number':
                            value = Math.sin(now.getTime() / 3500 + Math.PI/3) * 35 + 55 + Math.random() * 6;
                            break;
                        case 'master-slave':
                            value = Math.abs(Math.cos(now.getTime() / 2200)) * 60 + 20 + Math.random() * 7;
                            break;
                        case 'svg-connection':
                            value = Math.sin(now.getTime() / 1700) * 45 + 65 + Math.random() * 5;
                            break;
                        case 'balance-control':
                            value = Math.cos(now.getTime() / 2800 + Math.PI/6) * 30 + 40 + Math.random() * 4;
                            break;
                        case 'voltage-feedforward':
                            value = Math.sin(now.getTime() / 2100) * 50 + 70 + Math.random() * 8;
                            break;
                        case 'idle-mode':
                            value = Math.abs(Math.sin(now.getTime() / 3200)) * 40 + 30 + Math.random() * 3;
                            break;
                        default:
                            value = Math.sin(now.getTime() / 1000 + Math.random()) * 50 + Math.random() * 20;
                    }

                    chartData[paramId].push([now, value]);

                    // 保持数据点数量在合理范围内
                    if (chartData[paramId].length > 100) {
                        chartData[paramId].shift();
                    }
                });

                // 更新图表
                if (parameterChart && Object.keys(chartData).length > 0) {
                    const option = parameterChart.getOption();
                    const seriesData = Object.keys(chartData).map(paramId => {
                        const param = parameters.find(p => p.id === paramId);
                        return {
                            name: param ? param.name : paramId,
                            type: 'line',
                            data: chartData[paramId],
                            lineStyle: { color: param ? param.color : '#ffffff' },
                            itemStyle: { color: param ? param.color : '#ffffff' },
                            smooth: true,
                            symbol: 'none',
                            animation: false
                        };
                    });

                    option.series = seriesData;
                    parameterChart.setOption(option, false, true);
                }
            }, 1000);
        }

        /**
         * 暂停/恢复图表更新
         */
        function pauseChart() {
            isPaused = !isPaused;
            const btn = event.target.closest('.chart-control-btn');
            if (btn) {
                const icon = btn.querySelector('i');
                const text = btn.querySelector('span') || btn.childNodes[1];
                
                if (isPaused) {
                    icon.className = 'fas fa-play';
                    if (text) text.textContent = ' 继续';
                } else {
                    icon.className = 'fas fa-pause';
                    if (text) text.textContent = ' 暂停';
                }
            }
        }

        /**
         * 重置图表数据
         */
        function resetChart() {
            chartData = {};
            if (parameterChart) {
                const option = parameterChart.getOption();
                option.series = [];
                parameterChart.setOption(option);
            }
            
            // 重置所有参数开关状态
            document.querySelectorAll('.parameter-switch.active').forEach(el => {
                el.classList.remove('active');
            });
        }

        /**
         * 导出图表数据
         */
        function exportChart() {
            if (!parameterChart) return;
            
            // 导出为图片
            const url = parameterChart.getDataURL({
                type: 'png',
                backgroundColor: '#1a1f2e'
            });
            
            const link = document.createElement('a');
            link.download = `参数曲线_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`;
            link.href = url;
            link.click();
        }
    </script>
</body>
</html>
