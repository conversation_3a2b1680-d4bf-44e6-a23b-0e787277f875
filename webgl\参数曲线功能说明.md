# 参数曲线功能说明 (v2.0)

## 功能概述

参数曲线功能模块为桂林智源SVG数字化系统提供了实时参数监控和曲线显示功能。用户可以选择不同的曲线类型，配置相应的参数控制方式，并实时查看参数变化趋势。该模块支持实时模式和历史模式两种数据查看方式，所有参数均为数字信号（0/1状态）。

## 主要特性

### 1. 时间筛选功能
系统提供两种时间模式：

**实时模式（默认）：**
- 显示当前实时数据流
- 数据每秒更新一次
- 动态生成0/1数字信号

**历史模式：**
- 提供预设的具体历史时间点选择
- 8个预设历史时间点可选
- 显示对应时间段的固定历史数据模式

### 2. 曲线类型支持
系统支持以下11种曲线类型（下拉框选择）：

- IO状态输入1
- IO状态输入2
- IO状态输出1
- IO状态输出2
- 系统状态
- DSP状态1
- DSP状态2
- DSP状态3
- 直流电压
- 直接电流
- 间接电流有功

### 3. 参数监控（统一开关型控制）
当选择特定曲线类型后，系统会显示以下12个参数的控制选项：

**所有参数均为开关型控制，支持两种状态：**
- **开启状态**：值为1（在图表中显示为高电平）
- **关闭状态**：值为0（在图表中显示为低电平）

**参数列表：**
1. **手动给定无功** - 红色曲线 (#ff6b6b)
2. **锁相环模式** - 青色曲线 (#4ecdc4)
3. **控制模式选择** - 蓝色曲线 (#45b7d1)
4. **低电压无功支撑** - 绿色曲线 (#96ceb4)
5. **单元级数** - 黄色曲线 (#feca57)
6. **CT采样点位置** - 粉色曲线 (#ff9ff3)
7. **相数** - 天蓝色曲线 (#54a0ff)
8. **主从机模式** - 紫色曲线 (#5f27cd)
9. **SVG连接方式** - 青绿色曲线 (#00d2d3)
10. **平衡控制使能** - 橙红色曲线 (#ff6348)
11. **电压前馈模式** - 绿色曲线 (#2ed573)
12. **空载模式使能** - 橙色曲线 (#ffa502)

### 4. 界面布局

#### 左侧控制面板（350px宽度）
- **时间筛选区域**：选择实时模式或历史模式，历史模式可选择具体时间点
- **曲线类型选择区域**：下拉框选择曲线类型，默认未选择任何类型
- **参数控制区域**：根据选择的曲线类型动态显示对应的参数控制选项
- **状态指示器**：显示当前选择的曲线类型和参数数量
- **帮助信息**：为用户提供操作指导

#### 右侧图表区域（自适应宽度）
- **图表标题栏**：显示"实时参数曲线"标题和控制按钮
- **控制按钮**：
  - 暂停/继续：控制数据更新
  - 重置：清空所有数据和选择
  - 导出：将图表导出为PNG图片
- **ECharts图表容器**：显示实时参数曲线

## 使用方法

### 基本操作流程

1. **选择时间模式**
   - 默认为"实时模式"，显示当前实时数据流
   - 点击"历史模式"可切换到历史数据查看
   - 历史模式下可从下拉框选择具体的历史时间点

2. **选择曲线类型**
   - 在"曲线类型选择"下拉框中选择一个曲线类型
   - 页面加载时默认未选择任何类型
   - 选择新类型时会自动清空之前的参数设置

3. **配置参数控制**
   - 选择曲线类型后，下方会显示对应的参数控制选项
   - **所有参数均为开关型控制**：点击开关按钮控制参数曲线的显示/隐藏
   - 开关开启时显示该参数的数字信号曲线（0/1状态）

4. **查看数字信号曲线**
   - 激活的参数会在右侧图表中显示对应颜色的数字信号曲线
   - 每个参数都有独特的颜色标识，与左侧控制面板中的颜色圆点对应
   - Y轴显示0（关闭）和1（开启）两种状态
   - 曲线以阶梯线形式显示，更适合数字信号

5. **图表控制**
   - **暂停/继续**：暂停或恢复数据更新
   - **重置**：清空所有数据，重置所有选择状态
   - **导出**：将当前图表导出为PNG格式图片

### 高级功能

#### 数据模拟
系统为每个参数提供了不同的数字信号模拟模式：

**实时模式：**
- 基于正弦/余弦函数生成动态的0/1信号
- 每个参数有不同的切换频率和阈值
- 模拟真实的数字信号变化特征

**历史模式：**
- 为每个参数提供固定的历史数据模式
- 不同历史时间点显示相同的预设模式
- 10个数据点的循环模式

#### 响应式设计
- 页面尺寸固定为1366×768像素，适配大屏显示
- 图表支持窗口大小变化时的自动调整
- 左侧控制面板支持滚动，适应不同数量的参数显示

#### 视觉效果
- 深色科技主题，符合工业监控界面风格
- 科技感背景动画效果
- 平滑的过渡动画和交互反馈
- 专业的颜色搭配和图标设计

## 技术实现

### 前端技术栈
- **HTML5 + CSS3**：页面结构和样式
- **JavaScript ES6**：交互逻辑和数据处理
- **ECharts 5.4.3**：图表可视化
- **Font Awesome 6.0**：图标库

### 核心功能模块
- **曲线类型管理**：支持不同类型的参数控制方式
- **参数配置系统**：动态生成参数控制界面
- **实时数据更新**：模拟真实的参数变化
- **图表渲染引擎**：基于ECharts的高性能图表显示
- **数据导出功能**：支持图表导出为图片格式

### 性能优化
- 数据点数量限制（最多100个点）
- 图表更新频率控制（1秒间隔）
- 内存管理和垃圾回收优化
- 响应式图表大小调整

## 集成说明

参数曲线功能已完全集成到主系统中：

1. **菜单集成**：在main.html的下拉菜单中添加了"参数曲线"选项
2. **弹窗显示**：通过iframe方式在1366×768像素弹窗中显示
3. **样式一致性**：采用与主系统相同的深色科技主题
4. **交互体验**：保持与其他功能模块一致的操作方式

## 扩展建议

1. **数据源集成**：连接真实的设备数据源
2. **历史数据查询**：添加历史数据查看功能
3. **报警阈值设置**：为参数设置报警阈值
4. **数据导出格式**：支持更多导出格式（CSV、Excel等）
5. **用户配置保存**：保存用户的参数配置偏好
6. **多曲线类型同时显示**：支持同时选择多个曲线类型

## 故障排除

### 常见问题
1. **图表不显示**：检查ECharts库是否正确加载
2. **数据不更新**：确认JavaScript没有错误，检查控制台日志
3. **样式异常**：确认CSS文件正确引用
4. **导出失败**：检查浏览器是否支持Canvas导出功能

### 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

建议使用最新版本的现代浏览器以获得最佳体验。
