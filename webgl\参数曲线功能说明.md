# 参数曲线功能说明

## 功能概述

参数曲线功能模块为桂林智源SVG数字化系统提供了实时参数监控和曲线显示功能。用户可以选择不同的曲线类型，配置相应的参数控制方式，并实时查看参数变化趋势。

## 主要特性

### 1. 曲线类型支持
系统支持以下11种曲线类型：

**开关型控制（直接开关显示/隐藏）：**
- IO状态输入1
- IO状态输入2  
- IO状态输出1
- IO状态输出2
- 系统状态
- DSP状态1
- DSP状态2
- DSP状态3

**范围型控制（需要设置上下限值）：**
- 直流电压
- 直接电流
- 间接电流有功

### 2. 参数监控
当选择特定曲线类型后，系统会显示以下12个参数的控制选项：

1. **手动给定无功** - 红色曲线 (#ff6b6b)
2. **锁相环模式** - 青色曲线 (#4ecdc4)
3. **控制模式选择** - 蓝色曲线 (#45b7d1)
4. **低电压无功支撑** - 绿色曲线 (#96ceb4)
5. **单元级数** - 黄色曲线 (#feca57)
6. **CT采样点位置** - 粉色曲线 (#ff9ff3)
7. **相数** - 天蓝色曲线 (#54a0ff)
8. **主从机模式** - 紫色曲线 (#5f27cd)
9. **SVG连接方式** - 青绿色曲线 (#00d2d3)
10. **平衡控制使能** - 橙红色曲线 (#ff6348)
11. **电压前馈模式** - 绿色曲线 (#2ed573)
12. **空载模式使能** - 橙色曲线 (#ffa502)

### 3. 界面布局

#### 左侧控制面板（350px宽度）
- **曲线类型选择区域**：显示所有可用的曲线类型，支持单选
- **参数控制区域**：根据选择的曲线类型动态显示对应的参数控制选项
- **状态指示器**：显示当前选择的曲线类型和参数数量
- **帮助信息**：为用户提供操作指导

#### 右侧图表区域（自适应宽度）
- **图表标题栏**：显示"实时参数曲线"标题和控制按钮
- **控制按钮**：
  - 暂停/继续：控制数据更新
  - 重置：清空所有数据和选择
  - 导出：将图表导出为PNG图片
- **ECharts图表容器**：显示实时参数曲线

## 使用方法

### 基本操作流程

1. **选择曲线类型**
   - 在左侧"曲线类型选择"区域点击任意一个曲线类型
   - 选中的类型会高亮显示，并显示复选框勾选状态
   - 同时只能选择一个曲线类型

2. **配置参数控制**
   - 选择曲线类型后，下方会显示对应的参数控制选项
   - **开关型参数**：点击开关按钮控制参数曲线的显示/隐藏
   - **范围型参数**：在输入框中设置参数的上下限值

3. **查看实时曲线**
   - 激活的参数会在右侧图表中显示对应颜色的实时曲线
   - 每个参数都有独特的颜色标识，与左侧控制面板中的颜色圆点对应
   - 曲线会实时更新，显示参数的变化趋势

4. **图表控制**
   - **暂停/继续**：暂停或恢复数据更新
   - **重置**：清空所有数据，重置所有选择状态
   - **导出**：将当前图表导出为PNG格式图片

### 高级功能

#### 数据模拟
系统为每个参数提供了不同的数据模拟模式：
- 不同的正弦/余弦波形组合
- 不同的频率和幅度
- 随机噪声叠加
- 模拟真实的工业参数变化特征

#### 响应式设计
- 页面尺寸固定为1366×768像素，适配大屏显示
- 图表支持窗口大小变化时的自动调整
- 左侧控制面板支持滚动，适应不同数量的参数显示

#### 视觉效果
- 深色科技主题，符合工业监控界面风格
- 科技感背景动画效果
- 平滑的过渡动画和交互反馈
- 专业的颜色搭配和图标设计

## 技术实现

### 前端技术栈
- **HTML5 + CSS3**：页面结构和样式
- **JavaScript ES6**：交互逻辑和数据处理
- **ECharts 5.4.3**：图表可视化
- **Font Awesome 6.0**：图标库

### 核心功能模块
- **曲线类型管理**：支持不同类型的参数控制方式
- **参数配置系统**：动态生成参数控制界面
- **实时数据更新**：模拟真实的参数变化
- **图表渲染引擎**：基于ECharts的高性能图表显示
- **数据导出功能**：支持图表导出为图片格式

### 性能优化
- 数据点数量限制（最多100个点）
- 图表更新频率控制（1秒间隔）
- 内存管理和垃圾回收优化
- 响应式图表大小调整

## 集成说明

参数曲线功能已完全集成到主系统中：

1. **菜单集成**：在main.html的下拉菜单中添加了"参数曲线"选项
2. **弹窗显示**：通过iframe方式在1366×768像素弹窗中显示
3. **样式一致性**：采用与主系统相同的深色科技主题
4. **交互体验**：保持与其他功能模块一致的操作方式

## 扩展建议

1. **数据源集成**：连接真实的设备数据源
2. **历史数据查询**：添加历史数据查看功能
3. **报警阈值设置**：为参数设置报警阈值
4. **数据导出格式**：支持更多导出格式（CSV、Excel等）
5. **用户配置保存**：保存用户的参数配置偏好
6. **多曲线类型同时显示**：支持同时选择多个曲线类型

## 故障排除

### 常见问题
1. **图表不显示**：检查ECharts库是否正确加载
2. **数据不更新**：确认JavaScript没有错误，检查控制台日志
3. **样式异常**：确认CSS文件正确引用
4. **导出失败**：检查浏览器是否支持Canvas导出功能

### 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

建议使用最新版本的现代浏览器以获得最佳体验。
